#!/usr/bin/env python3
"""
测试LoRA冻结配置脚本
验证只有action expert的LoRA参数会被训练，其他参数都被冻结
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

import flax.nnx as nnx
import jax
from openpi.models import pi0
from openpi.shared import nnx_utils

def create_action_expert_only_freeze_filter():
    """
    创建自定义的freeze_filter，只训练action expert的LoRA参数，冻结所有其他参数
    """
    # 只保留action expert的LoRA参数可训练（.*llm.*_1.*lora.*）
    action_expert_lora_pattern = nnx_utils.PathRegex(".*llm.*_1.*lora.*")

    # 返回：冻结所有不是action expert LoRA的参数
    return nnx.Not(action_expert_lora_pattern)

def test_freeze_configuration():
    """测试不同配置下的参数冻结情况"""
    
    print("🧪 测试LoRA参数冻结配置")
    print("=" * 60)
    
    # 测试1: 原始配置（同时训练两个LoRA）
    print("\n1️⃣ 原始配置 - 同时训练PaliGemma 2B LoRA + Action Expert 300M LoRA:")
    config_both = pi0.Pi0Config(
        action_dim=32,
        action_horizon=50,
        max_token_len=48,
        paligemma_variant="gemma_2b_lora",
        action_expert_variant="gemma_300m_lora"
    )
    
    abstract_model = nnx.eval_shape(config_both.create, jax.random.key(0))
    freeze_filter = config_both.get_freeze_filter()
    frozen_state = nnx.state(abstract_model, nnx.All(nnx.Param, freeze_filter)).flat_state()
    
    print(f"   冻结参数数量: {len(frozen_state)}")
    print("   冻结参数类型:")
    for param_path in list(frozen_state.keys())[:5]:  # 只显示前5个
        print(f"     - {param_path}")
    if len(frozen_state) > 5:
        print(f"     ... 还有 {len(frozen_state) - 5} 个参数")
    
    # 测试2: 新配置（只训练action expert LoRA）
    print("\n2️⃣ 新配置 - 只训练Action Expert 300M LoRA:")
    config_action_only = pi0.Pi0Config(
        action_dim=32,
        action_horizon=50,
        max_token_len=48,
        paligemma_variant="gemma_2b",          # 不使用LoRA，完全冻结
        action_expert_variant="gemma_300m_lora"  # 只有这个使用LoRA
    )
    
    abstract_model = nnx.eval_shape(config_action_only.create, jax.random.key(0))
    freeze_filter = config_action_only.get_freeze_filter()
    frozen_state = nnx.state(abstract_model, nnx.All(nnx.Param, freeze_filter)).flat_state()
    
    print(f"   冻结参数数量: {len(frozen_state)}")
    print("   冻结参数类型:")
    for param_path in list(frozen_state.keys())[:5]:  # 只显示前5个
        print(f"     - {param_path}")
    if len(frozen_state) > 5:
        print(f"     ... 还有 {len(frozen_state) - 5} 个参数")
    
    # 验证冻结的参数都不包含lora
    lora_in_frozen = [p for p in frozen_state.keys() if "lora" in p]
    print(f"   冻结参数中包含'lora'的数量: {len(lora_in_frozen)}")
    
    # 验证冻结的参数都包含llm（语言模型参数）
    llm_in_frozen = [p for p in frozen_state.keys() if "llm" in p]
    print(f"   冻结参数中包含'llm'的数量: {len(llm_in_frozen)}")
    
    # 验证action expert参数（包含_1）
    action_expert_in_frozen = [p for p in frozen_state.keys() if "_1" in p]
    print(f"   冻结参数中包含'_1'(action expert)的数量: {len(action_expert_in_frozen)}")

    # 测试3: 自定义配置（只训练action expert LoRA）
    print("\n3️⃣ 自定义配置 - 使用自定义freeze_filter只训练Action Expert LoRA:")

    abstract_model = nnx.eval_shape(config_action_only.create, jax.random.key(0))
    custom_freeze_filter = create_action_expert_only_freeze_filter()
    custom_frozen_state = nnx.state(abstract_model, nnx.All(nnx.Param, custom_freeze_filter)).flat_state()

    print(f"   冻结参数数量: {len(custom_frozen_state)}")
    print("   冻结参数类型:")
    for param_path in list(custom_frozen_state.keys())[:5]:  # 只显示前5个
        print(f"     - {param_path}")
    if len(custom_frozen_state) > 5:
        print(f"     ... 还有 {len(custom_frozen_state) - 5} 个参数")

    # 验证自定义配置
    custom_lora_in_frozen = [p for p in custom_frozen_state.keys() if "lora" in p]
    custom_action_expert_lora_in_frozen = [p for p in custom_frozen_state.keys() if "lora" in p and "_1" in p]

    print(f"   冻结参数中包含'lora'的数量: {len(custom_lora_in_frozen)}")
    print(f"   冻结参数中包含'_1'和'lora'的数量: {len(custom_action_expert_lora_in_frozen)}")

    print("\n4️⃣ 配置分析:")
    print("   自定义freeze_filter逻辑:")
    print("   - 使用 nnx.Not(nnx_utils.PathRegex('.*llm.*_1.*lora.*'))")
    print("   - 冻结所有不匹配 '.*llm.*_1.*lora.*' 的参数")
    print("   - 只有Action Expert的LoRA参数(.*llm.*_1.*lora.*)可以训练")
    print("   - 所有其他参数（PaliGemma 2B、图像编码器、非LoRA参数）都被冻结")

    print("\n✅ 配置验证完成!")
    print("   自定义配置确保只训练Action Expert的LoRA参数，其他所有参数都被冻结")
    
    return len(custom_frozen_state)

def analyze_trainable_parameters():
    """分析可训练参数"""
    print("\n🔍 分析可训练参数:")
    print("=" * 60)
    
    config = pi0.Pi0Config(
        action_dim=32,
        action_horizon=50,
        max_token_len=48,
        paligemma_variant="gemma_2b",          # 完全冻结
        action_expert_variant="gemma_300m_lora"  # 只训练LoRA
    )

    abstract_model = nnx.eval_shape(config.create, jax.random.key(0))

    # 获取所有参数
    all_params = nnx.state(abstract_model, nnx.Param).flat_state()
    print(f"总参数数量: {len(all_params)}")

    # 使用自定义freeze_filter获取冻结参数
    custom_freeze_filter = create_action_expert_only_freeze_filter()
    frozen_params = nnx.state(abstract_model, nnx.All(nnx.Param, custom_freeze_filter)).flat_state()
    print(f"冻结参数数量: {len(frozen_params)}")
    
    # 计算可训练参数
    trainable_count = len(all_params) - len(frozen_params)
    print(f"可训练参数数量: {trainable_count}")
    
    # 找出可训练参数
    frozen_keys = set(frozen_params.keys())
    trainable_params = {k: v for k, v in all_params.items() if k not in frozen_keys}
    
    print(f"\n可训练参数列表 ({len(trainable_params)} 个):")
    for param_path in trainable_params.keys():
        print(f"  - {param_path}")
    
    # 验证所有可训练参数都是Action Expert的LoRA参数
    lora_trainable = [p for p in trainable_params.keys() if "lora" in p]
    action_expert_trainable = [p for p in trainable_params.keys() if "_1" in p]
    action_expert_lora_trainable = [p for p in trainable_params.keys() if "lora" in p and "_1" in p]

    print(f"\n参数分析:")
    print(f"  - 可训练参数中包含'lora'的数量: {len(lora_trainable)}")
    print(f"  - 可训练参数中包含'_1'(action expert)的数量: {len(action_expert_trainable)}")
    print(f"  - 可训练参数中同时包含'lora'和'_1'的数量: {len(action_expert_lora_trainable)}")

    if len(action_expert_lora_trainable) == len(trainable_params) and len(trainable_params) > 0:
        print("  ✅ 所有可训练参数都是Action Expert的LoRA参数!")
    else:
        print("  ❌ 存在非Action Expert LoRA的可训练参数!")
        print("  非Action Expert LoRA的可训练参数:")
        non_action_expert_lora = [p for p in trainable_params.keys() if not ("lora" in p and "_1" in p)]
        for param in non_action_expert_lora[:10]:  # 只显示前10个
            print(f"    - {param}")
        if len(non_action_expert_lora) > 10:
            print(f"    ... 还有 {len(non_action_expert_lora) - 10} 个参数")
    
    return trainable_count

if __name__ == "__main__":
    try:
        frozen_count = test_freeze_configuration()
        trainable_count = analyze_trainable_parameters()
        
        print("\n" + "=" * 60)
        print("📊 总结:")
        print(f"  - 冻结参数: {frozen_count}")
        print(f"  - 可训练参数: {trainable_count}")
        print(f"  - 训练策略: 只训练Action Expert的LoRA参数")
        print("  - 内存优势: PaliGemma 2B完全冻结，显著减少内存使用")
        print("✅ 配置测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
