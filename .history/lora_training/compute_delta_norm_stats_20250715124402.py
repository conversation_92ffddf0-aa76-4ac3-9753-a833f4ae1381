#!/usr/bin/env python3
"""
为茄子数据集重新计算基于delta actions和delta states的norm stats
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from compute_norm_stats import main

if __name__ == "__main__":
    # 茄子数据集路径
    data_path = "/home/<USER>/data/pick_and_place_eggplant/openpi"
    dataset_name = "pick_and_place_eggplant"

    print("🚀 开始计算基于delta actions和delta states的norm stats...")
    print(f"数据路径: {data_path}")
    print(f"数据集名称: {dataset_name}")
    print("模式: Delta actions + Delta states (相对关节角)")
    print()

    # 计算delta actions + delta states模式的norm stats
    success = main(
        data_path=data_path,
        dataset_name=dataset_name,
        use_delta_actions=True,
        use_delta_states=True
    )

    if success:
        print("\n🎉 Delta actions + Delta states norm stats计算完成！")
        print("现在可以使用新的norm stats进行训练了")
    else:
        print("\n❌ 计算失败，请检查错误信息")
