#!/usr/bin/env python3
"""
直接推理工具 - 无需服务器，直接加载模型进行推理

使用示例:
conda activate openpi && python inference_piper/direct_inference.py \
    --checkpoint_dir checkpoints/lora_training/ae_lora_adamw_delta_action15_norm30_bat20_15w/62500 \
    --config_name lora_training \
    --default_prompt "pass me the drink" \
    --dataset_name "pass_drink" \
    --data_path "/home/<USER>/data/pass_drink/openpi" \
    --episode 10 \
    --step_limit 200 \
    --output inference_result/pass_drink_direct.html \
    --future_steps 15 \
    --prediction_step 14 \
    --action_horizon 15 \
    --multi_step_plot \
    --debug
"""

import os
import sys
import time
from pathlib import Path

# 获取当前文件的绝对路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_file))
sys.path.append(project_root)

import logging
import dataclasses
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image
import io
import argparse
import traceback

# 设置环境变量
os.environ['XLA_PYTHON_CLIENT_MEM_FRACTION'] = '0.7'
os.environ['XLA_PYTHON_CLIENT_PREALLOCATE'] = 'false'

# 导入OpenPI模块
from openpi.training import config as _config
from openpi.policies import policy_config as _policy_config
from openpi.models import pi0

# 设置日志
logging.basicConfig(level=logging.INFO, force=True)
logger = logging.getLogger(__name__)

# 尝试导入plotly用于HTML可视化
try:
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    import plotly.offline as pyo
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False
    logger.warning("Plotly未安装，将使用matplotlib生成PNG图表")

# 关节名称定义
JOINT_NAMES = [
    "Left Arm J0", "Left Arm J1", "Left Arm J2", "Left Arm J3", "Left Arm J4", "Left Arm J5", "Left Gripper",
    "Right Arm J0", "Right Arm J1", "Right Arm J2", "Right Arm J3", "Right Arm J4", "Right Arm J5", "Right Gripper"
]

@dataclasses.dataclass
class DirectInferenceConfig:
    """直接推理配置"""
    # 模型配置
    checkpoint_dir: str
    config_name: str
    default_prompt: str
    dataset_name: str
    data_path: str
    action_horizon: int = 15
    
    # 推理配置
    episode: int = 0
    step_limit: int = 200
    output: str = "direct_inference.png"
    future_steps: int = 15
    prediction_step: int = 14
    max_timesteps: int = 200
    prediction_horizon: int = 14
    multi_step_plot: bool = False
    max_errors: int = 1
    convert_bgr_to_rgb: bool = True
    debug: bool = False

def create_trained_model_config(config_name: str, dataset_name: str, data_path: str, default_prompt: str, action_horizon: int = 15):
    """创建与训练时相同的模型配置"""
    from lora_training.lora_train_config import CustomDataConfig
    from openpi.training.weight_loaders import CheckpointWeightLoader
    
    config = _config.TrainConfig(
        name=config_name,
        model=pi0.Pi0Config(action_dim=32, action_horizon=action_horizon),
        data=CustomDataConfig(
            data_path=data_path,
            dataset_name=dataset_name,
            default_prompt=default_prompt,
            use_delta_joint_actions=True,
        ),
        weight_loader=CheckpointWeightLoader("dummy"),  # 实际路径在create_policy中设置
        batch_size=1,
        num_train_steps=1,
        wandb_enabled=False,
    )
    return config

def load_normalization_stats(data_path: str, dataset_name: str, action_horizon: int = 15, checkpoint_dir: str = None):
    """加载归一化统计信息"""
    data_path_obj = Path(data_path)
    possible_paths = [
        f"./assets/{dataset_name}_del_step{action_horizon}/norm_stats.json",
        f"./assets/{data_path_obj.name}_del/norm_stats.json",
        f"./assets/{data_path_obj.name}_abs/norm_stats.json",
        "./assets/norm_stats.json",
    ]

    # 如果提供了检查点目录，也搜索检查点目录中的assets
    if checkpoint_dir:
        checkpoint_path = Path(checkpoint_dir)
        possible_paths.insert(0, str(checkpoint_path / "assets" / f"{dataset_name}_del_step{action_horizon}" / "norm_stats.json"))
        possible_paths.insert(1, str(checkpoint_path / "assets" / f"{data_path_obj.name}_del_step{action_horizon}" / "norm_stats.json"))
    
    for path in possible_paths:
        if Path(path).exists():
            try:
                from openpi.shared import normalize
                norm_stats = normalize.load(Path(path).parent)
                logger.info(f"成功加载归一化统计信息: {path}")
                return norm_stats
            except Exception as e:
                logger.warning(f"加载归一化统计信息失败: {e}")
                continue
    
    logger.warning("未找到归一化统计信息文件，将不进行反归一化")
    return None

def unnormalize_actions(actions, norm_stats):
    """反归一化动作数据"""
    if norm_stats is None or "actions" not in norm_stats:
        return actions
    
    stats = norm_stats["actions"]
    # 使用z-score反归一化: x = normalized_x * std + mean
    actions_array = np.array(actions)
    original_shape = actions_array.shape
    
    # 处理不同的数据形状
    if len(original_shape) == 3:  # (batch, horizon, dim)
        unnorm_actions = actions_array * (stats.std[:original_shape[-1]] + 1e-6) + stats.mean[:original_shape[-1]]
    elif len(original_shape) == 2:  # (horizon, dim)
        unnorm_actions = actions_array * (stats.std[:original_shape[-1]] + 1e-6) + stats.mean[:original_shape[-1]]
    else:  # (dim,)
        unnorm_actions = actions_array * (stats.std[:len(actions_array)] + 1e-6) + stats.mean[:len(actions_array)]
    
    return unnorm_actions

def decode_image(img_bytes, convert_bgr_to_rgb=True):
    """解码图像数据

    Args:
        img_bytes: 图像字节数据
        convert_bgr_to_rgb: 是否将BGR转换为RGB（如果原始数据是BGR格式）

    Returns:
        归一化到[-1,1]的float32图像数组
    """
    try:
        img = Image.open(io.BytesIO(img_bytes))
        img_array = np.array(img, dtype=np.float32) / 255.0

        # 如果需要BGR到RGB转换
        if convert_bgr_to_rgb and len(img_array.shape) == 3 and img_array.shape[2] == 3:
            img_array = img_array[:, :, ::-1]  # 反转颜色通道顺序

        # 归一化到[-1,1]范围
        return img_array * 2.0 - 1.0
    except Exception as e:
        logger.error(f"图像解码失败: {e}")
        return None

class DirectInferenceRunner:
    """直接推理执行器"""
    def __init__(self, config: DirectInferenceConfig):
        self.config = config
        self.policy = None
        self.norm_stats = None
        self.gt_data = None
        
    def load_model(self):
        """加载模型"""
        try:
            logger.info(f"正在加载模型...")
            logger.info(f"检查点目录: {self.config.checkpoint_dir}")
            logger.info(f"配置名称: {self.config.config_name}")
            logger.info(f"数据集名称: {self.config.dataset_name}")
            logger.info(f"Action Horizon: {self.config.action_horizon}")
            
            # 创建配置
            train_config = create_trained_model_config(
                self.config.config_name,
                self.config.dataset_name,
                self.config.data_path,
                self.config.default_prompt,
                self.config.action_horizon
            )
            
            # 创建训练好的策略
            self.policy = _policy_config.create_trained_policy(
                train_config,
                self.config.checkpoint_dir,
                default_prompt=self.config.default_prompt
            )
            
            # 加载归一化统计信息
            self.norm_stats = load_normalization_stats(
                self.config.data_path,
                self.config.dataset_name,
                self.config.action_horizon,
                self.config.checkpoint_dir
            )
            
            logger.info("模型加载成功!")
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            traceback.print_exc()
            return False
    
    def load_gt_data(self):
        """加载真实数据"""
        try:
            data_path = Path(self.config.data_path)
            parquet_files = list(data_path.glob("data/chunk-*/episode_*.parquet"))
            if not parquet_files:
                parquet_files = list(data_path.glob("episode_*.parquet"))
                if not parquet_files:
                    logger.error("未找到parquet文件")
                    return False
            
            # 加载指定episode的数据
            target_episode = self.config.episode
            episode_file = None
            
            for file_path in parquet_files:
                try:
                    df = pd.read_parquet(file_path)
                    if len(df) > 0:
                        episode_idx = int(file_path.stem.split('_')[-1])
                        if episode_idx == target_episode:
                            episode_file = file_path
                            break
                except Exception as e:
                    logger.warning(f"无法读取文件 {file_path}: {e}")
                    continue
            
            if episode_file is None:
                logger.error(f"未找到episode {target_episode}的数据")
                return False
            
            # 加载数据
            df = pd.read_parquet(episode_file)
            
            # 检查实际可用的相机和数据结构
            if self.config.debug and len(df) > 0:
                logger.info(f"Parquet文件结构分析:")
                logger.info(f"列名: {list(df.columns)}")

                # 检查状态向量维度
                if 'observation.state' in df.columns and len(df['observation.state']) > 0:
                    first_state = df['observation.state'].iloc[0]
                    logger.info(f"状态向量类型: {type(first_state)}")
                    logger.info(f"状态向量维度: {len(first_state) if isinstance(first_state, (list, tuple, np.ndarray)) else 'unknown'}")
                    if hasattr(first_state, '__len__') and len(first_state) > 0:
                        logger.info(f"状态向量前5个值: {first_state[:5]}")

            # 加载动作和状态数据 - 使用与inference_with_dis_openloop.py相同的方法
            actions = np.array([np.array(action)[:14] for action in df['action']])
            states = np.array([np.array(state)[:14] for state in df['observation.state']])

            # 收集图像数据（如果存在）
            image_columns = [col for col in df.columns if 'image' in col.lower() or 'rgb' in col.lower()]

            # 存储列名到数据字典中，用于后续格式确定
            column_names = list(df.columns)

            # 直接存储原始数据帧引用，这样我们在推理时可以直接访问
            self.gt_data = {
                'episode_file': episode_file.name,
                'actions': actions,
                'states': states,
                'df': df,  # 存储整个数据帧
                'image_columns': image_columns,  # 存储图像列名
                'column_names': column_names,  # 存储所有列名
                'length': len(actions)
            }
            
            logger.info(f"成功加载episode {target_episode}，共 {len(states)} 个时间步")
            logger.info(f"已加载 {episode_file.name}：{len(actions)}步，状态形状{states.shape}，动作形状{actions.shape}，图像列数量{len(image_columns)}")

            # 调试信息：检查前几个状态的结构
            if len(states) > 0:
                logger.info(f"第一个状态的类型: {type(states[0])}, 形状: {getattr(states[0], 'shape', 'N/A')}")
                if hasattr(states[0], '__len__'):
                    logger.info(f"第一个状态的长度: {len(states[0])}")
                    if len(states[0]) > 0:
                        logger.info(f"第一个状态的前5个值: {states[0][:5]}")

            # 检查图像数据
            logger.info(f"图像列: {image_columns}")
            if len(image_columns) == 0:
                logger.warning("没有找到图像列，将创建占位符图像")
                # 不直接返回错误，而是在推理时创建占位符图像

            return True
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            traceback.print_exc()
            return False

    def run_inference(self):
        """执行推理"""
        try:
            if not self.gt_data:
                logger.error("未加载数据")
                return None

            episode = self.gt_data
            states = episode['states']
            df = episode['df']
            image_columns = episode['image_columns']

            max_steps = min(self.config.step_limit, len(states) - self.config.future_steps)

            gt_states = []
            pred_states = []
            inference_times = []

            logger.info(f"开始推理，共 {max_steps} 步...")

            for step_idx in range(max_steps):
                try:
                    # 获取当前状态
                    current_state = states[step_idx]

                    # 确保current_state是numpy数组
                    if not isinstance(current_state, np.ndarray):
                        current_state = np.array(current_state, dtype=np.float32)

                    # 创建32维的填充状态
                    padded_state = np.zeros(32, dtype=np.float32)
                    state_len = min(len(current_state), 14)  # 最多取14维
                    padded_state[:state_len] = current_state[:state_len]

                    if self.config.debug and step_idx < 3:
                        logger.info(f"Step {step_idx}: current_state shape: {current_state.shape}, values: {current_state[:5]}")
                        logger.info(f"Step {step_idx}: padded_state shape: {padded_state.shape}, values: {padded_state[:5]}")

                    # 获取图像数据 - 直接从数据帧获取，与inference_with_dis_openloop.py一致
                    images = {}
                    if image_columns:
                        row = df.iloc[step_idx]
                        for col in image_columns:
                            if '.' in col:
                                cam_name = col.split('.')[-1]
                            else:
                                cam_name = col
                            img_data = row[col]
                            if img_data is not None:
                                if isinstance(img_data, dict) and 'bytes' in img_data:
                                    images[cam_name] = decode_image(img_data['bytes'], self.config.convert_bgr_to_rgb)
                                elif isinstance(img_data, bytes):
                                    images[cam_name] = decode_image(img_data, self.config.convert_bgr_to_rgb)
                                else:
                                    images[cam_name] = img_data

                    # 如果没有图像数据，创建占位符图像以满足PI0模型要求
                    if not images:
                        if self.config.debug:
                            logger.warning(f"Step {step_idx}: 没有找到图像数据，创建占位符图像")
                        # 创建一个黑色占位符图像 (480, 640, 3) 格式，值范围[-1, 1]
                        placeholder_image = np.zeros((480, 640, 3), dtype=np.float32)
                        images = {
                            "base_0_rgb": placeholder_image,  # PI0模型要求的基础图像
                        }

                    if self.config.debug and step_idx < 3:
                        logger.info(f"Step {step_idx}: 图像键: {list(images.keys())}")
                        for k, v in images.items():
                            logger.info(f"Step {step_idx}: image[{k}] shape: {v.shape}")

                    # 准备观测数据
                    obs = {
                        "state": padded_state,
                        "image": images,
                        "prompt": self.config.default_prompt,
                        "tokenized_prompt": np.zeros((1, 48), dtype=np.int32),
                        "tokenized_prompt_mask": np.ones((1, 48), dtype=bool)
                    }

                    # 执行推理
                    start_time = time.time()
                    result = self.policy.infer(obs)
                    inference_time = time.time() - start_time

                    # 获取预测动作
                    if "actions" in result:
                        pred_actions = result["actions"]

                        # 反归一化
                        if self.norm_stats is not None:
                            pred_actions = unnormalize_actions(pred_actions, self.norm_stats)

                        # 记录结果
                        gt_states.append(current_state)
                        pred_states.append(pred_actions)
                        inference_times.append(inference_time)

                        if step_idx % 10 == 0:
                            logger.info(f"步骤 {step_idx}/{max_steps} 完成，推理时间: {inference_time:.3f}s")

                except Exception as e:
                    logger.error(f"步骤 {step_idx} 推理失败: {e}")
                    if self.config.debug:
                        traceback.print_exc()
                    continue

            if not gt_states or not pred_states:
                logger.error("没有成功的推理步骤")
                return None

            results = {
                'gt_states': np.array(gt_states),
                'pred_states': np.array(pred_states),
                'inference_times': np.array(inference_times),
                'episode_idx': self.config.episode,
                'total_steps': len(gt_states),
                'future_steps': self.config.future_steps
            }

            logger.info(f"推理完成: {len(gt_states)} 步成功")
            return results

        except Exception as e:
            logger.error(f"推理执行失败: {e}")
            traceback.print_exc()
            return None

    def create_visualization(self, results):
        """创建可视化"""
        try:
            logger.info(f"PLOTLY_AVAILABLE: {PLOTLY_AVAILABLE}")
            if PLOTLY_AVAILABLE:
                logger.info("使用Plotly创建HTML可视化")
                self.create_html_visualization(results)
            else:
                logger.info("使用matplotlib创建PNG可视化")
                self.create_matplotlib_visualization(results)

        except Exception as e:
            logger.error(f"可视化创建失败: {e}")
            traceback.print_exc()

    def create_html_visualization(self, results):
        """创建HTML交互式可视化 - 处理delta值"""
        gt_states = results['gt_states']
        pred_states = results['pred_states']

        # 处理预测数据形状
        if len(pred_states.shape) == 3:  # (steps, horizon, dim)
            pred_step = min(self.config.prediction_step, pred_states.shape[1] - 1)
            pred_actions = pred_states[:, pred_step, :14]
        else:  # (steps, dim)
            pred_actions = pred_states[:, :14]

        # 注意：gt_states是当前状态，pred_actions是delta值（动作变化量）
        current_states = gt_states[:, :14]  # 当前关节角度
        delta_actions = pred_actions  # 预测的delta值

        # 计算真实的下一步状态（用于对比）
        # 这里我们需要从数据中获取真实的下一步状态
        episode = self.gt_data
        all_states = episode['states']

        # 计算真实的delta值（下一步状态 - 当前状态）
        gt_deltas = []
        for i in range(len(current_states)):
            if i + 1 < len(all_states):
                gt_delta = all_states[i + 1][:14] - current_states[i]
            else:
                gt_delta = np.zeros(14)  # 最后一步没有下一步，用零填充
            gt_deltas.append(gt_delta)
        gt_deltas = np.array(gt_deltas)

        errors = np.abs(delta_actions - gt_deltas)
        steps = np.arange(len(current_states))

        # 创建子图：14个关节，每个关节一行
        fig = make_subplots(
            rows=14, cols=1,
            subplot_titles=[f'{JOINT_NAMES[i]}' for i in range(14)],
            shared_xaxes=True,
            vertical_spacing=0.02
        )

        # 为每个关节创建图表
        for joint_idx in range(14):
            joint_name = JOINT_NAMES[joint_idx]

            # 每3步画一个点，并用折线连接
            step_indices = np.arange(0, len(steps), 3)
            gt_sampled = gt_actions[step_indices, joint_idx]
            pred_sampled = pred_actions[step_indices, joint_idx]
            steps_sampled = steps[step_indices]

            # 添加真实值轨迹
            fig.add_trace(
                go.Scatter(
                    x=steps_sampled,
                    y=gt_sampled,
                    mode='lines+markers',
                    name=f'GT {joint_name}' if joint_idx == 0 else None,
                    line=dict(color='royalblue', width=2),
                    marker=dict(size=4, symbol='circle'),
                    showlegend=(joint_idx == 0),
                    legendgroup='gt'
                ),
                row=joint_idx+1, col=1
            )

            # 添加预测值轨迹
            fig.add_trace(
                go.Scatter(
                    x=steps_sampled,
                    y=pred_sampled,
                    mode='lines+markers',
                    name=f'Pred {joint_name}' if joint_idx == 0 else None,
                    line=dict(color='orangered', width=2, dash='dash'),
                    marker=dict(size=4, symbol='square'),
                    showlegend=(joint_idx == 0),
                    legendgroup='pred'
                ),
                row=joint_idx+1, col=1
            )

            # 计算误差统计
            mse = np.mean((pred_sampled - gt_sampled) ** 2)
            mae = np.mean(np.abs(pred_sampled - gt_sampled))

            # 更新y轴标签
            fig.update_yaxes(title_text=f"Angle (rad)<br>MAE: {mae:.3f}", row=joint_idx+1, col=1)

        # 更新布局
        fig.update_layout(
            height=3500,  # 14个子图的高度
            title=dict(
                text=f'Episode {self.config.episode} Direct Inference Results<br>'
                     f'({results["total_steps"]} steps, Avg: {np.mean(results["inference_times"]):.3f}s/step, '
                     f'{1/np.mean(results["inference_times"]):.1f} Hz)',
                x=0.5,
                font=dict(size=16)
            ),
            showlegend=True,
            legend=dict(x=0.02, y=0.98),
            margin=dict(l=80, r=50, t=100, b=50)
        )

        # 更新x轴标签（只在最后一个子图）
        fig.update_xaxes(title_text="Timestep", row=14, col=1)

        # 保存为HTML文件
        html_output = self.config.output
        if not html_output.endswith('.html'):
            html_output = html_output.replace('.png', '.html')
        pyo.plot(fig, filename=html_output, auto_open=False)
        logger.info(f"HTML可视化结果保存到: {html_output}")

        # 打印统计信息
        logger.info(f"平均绝对误差: {np.mean(errors):.4f} rad ({np.degrees(np.mean(errors)):.2f} deg)")
        logger.info(f"平均推理时间: {np.mean(results['inference_times']):.3f}s")

    def create_matplotlib_visualization(self, results):
        """创建matplotlib可视化（备用方案）"""
        gt_states = results['gt_states']
        pred_states = results['pred_states']

        # 处理预测数据形状
        if len(pred_states.shape) == 3:  # (steps, horizon, dim)
            pred_step = min(self.config.prediction_step, pred_states.shape[1] - 1)
            pred_actions = pred_states[:, pred_step, :14]
        else:  # (steps, dim)
            pred_actions = pred_states[:, :14]

        gt_actions = gt_states[:, :14]
        errors = np.abs(pred_actions - gt_actions)
        steps = np.arange(len(gt_actions))

        # 创建14个关节的详细对比图，与原始代码风格一致
        fig, axes = plt.subplots(14, 1, figsize=(24, 3.5*14), sharex=True)
        fig.subplots_adjust(hspace=0.35)
        fig.suptitle(
            f'Episode {self.config.episode} Direct Inference Results\n'
            f'({results["total_steps"]} steps, Avg: {np.mean(results["inference_times"]):.3f}s/step, '
            f'{1/np.mean(results["inference_times"]):.1f} Hz)',
            fontsize=22, fontweight='bold'
        )

        for joint_idx in range(14):
            ax = axes[joint_idx]
            joint_name = JOINT_NAMES[joint_idx]

            try:
                gt_values = gt_actions[:, joint_idx]
                pred_values = pred_actions[:, joint_idx]

                # 每3步画一个点，并用折线连接
                step_indices = np.arange(0, len(steps), 3)  # 每3步取一个点
                gt_sampled = gt_values[step_indices]
                pred_sampled = pred_values[step_indices]
                steps_sampled = steps[step_indices]

                ax.plot(steps_sampled, gt_sampled, color='royalblue', linewidth=2.5,
                       label='Ground Truth', marker='o', markersize=4)
                ax.plot(steps_sampled, pred_sampled, color='orangered', linewidth=2.5,
                       label='Prediction', linestyle='--', marker='s', markersize=4)

                mse = np.mean((pred_sampled - gt_sampled) ** 2)
                mae = np.mean(np.abs(pred_sampled - gt_sampled))

                ax.set_title(f'{joint_name}\nMSE: {mse:.4f}, MAE: {mae:.4f}', fontsize=16)
                ax.grid(True, alpha=0.3)
                ax.legend(loc='upper right', fontsize=10)

                # 设置y轴范围
                y_min, y_max = ax.get_ylim()
                y_range = y_max - y_min
                ax.set_ylim(y_min - 0.1 * y_range, y_max + 0.1 * y_range)

            except IndexError as e:
                logger.warning(f"关节 {joint_idx} 数据索引错误: {e}")
                ax.set_title(f'{joint_name}\nData unavailable', fontsize=16)
                ax.text(0.5, 0.5, 'Index error', ha='center', va='center', transform=ax.transAxes)
                continue

        axes[-1].set_xlabel('Timestep', fontsize=16)

        # 确保输出文件是PNG格式
        png_output = self.config.output
        if png_output.endswith('.html'):
            png_output = png_output.replace('.html', '.png')
        elif not png_output.endswith('.png'):
            png_output += '.png'

        plt.tight_layout(rect=[0, 0, 1, 0.97])
        plt.savefig(png_output, dpi=200, bbox_inches='tight')
        plt.close()
        logger.info(f"可视化结果保存到: {png_output}")

        # 打印统计信息
        logger.info(f"平均绝对误差: {np.mean(errors):.4f} rad ({np.degrees(np.mean(errors)):.2f} deg)")
        logger.info(f"平均推理时间: {np.mean(results['inference_times']):.3f}s")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="直接推理工具")
    
    # 模型配置
    parser.add_argument("--checkpoint_dir", required=True, help="检查点目录路径")
    parser.add_argument("--config_name", required=True, help="配置名称")
    parser.add_argument("--default_prompt", required=True, help="默认提示词")
    parser.add_argument("--dataset_name", required=True, help="数据集名称")
    parser.add_argument("--data_path", required=True, help="数据集路径")
    parser.add_argument("--action_horizon", type=int, default=15, help="动作视野长度")
    
    # 推理配置
    parser.add_argument("--episode", type=int, default=0, help="要分析的剧集索引")
    parser.add_argument("--step_limit", type=int, default=200, help="步骤限制")
    parser.add_argument("--output", default="direct_inference.png", help="输出图像文件")
    parser.add_argument("--future_steps", type=int, default=15, help="未来步数")
    parser.add_argument("--prediction_step", type=int, default=14, help="预测步数")
    parser.add_argument("--max_timesteps", type=int, default=200, help="最大时间步")
    parser.add_argument("--prediction_horizon", type=int, default=14, help="预测视野")
    parser.add_argument("--multi_step_plot", action="store_true", help="创建多步预测图")
    parser.add_argument("--max_errors", type=int, default=1, help="最大错误数")
    parser.add_argument("--convert_bgr_to_rgb", action="store_true", default=True, help="BGR转RGB")
    parser.add_argument("--debug", action="store_true", help="调试模式")
    
    args = parser.parse_args()
    
    # 创建配置
    config = DirectInferenceConfig(**vars(args))
    
    # 创建推理执行器
    runner = DirectInferenceRunner(config)
    
    try:
        # 加载模型
        if not runner.load_model():
            return 1
        
        # 加载数据
        if not runner.load_gt_data():
            return 1
        
        # 执行推理
        results = runner.run_inference()
        if results is None:
            logger.error("推理失败")
            return 1

        # 生成可视化
        runner.create_visualization(results)

        logger.info("推理完成!")
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断")
        return 1
    except Exception as e:
        logger.error(f"执行失败: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
