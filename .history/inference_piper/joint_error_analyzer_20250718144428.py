#!/usr/bin/env python3
"""
关节角误差分析器
专门用于分析OpenPI推理结果中的关节角误差，提供详细的量化分析和可视化
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pandas as pd
import logging
from typing import Dict, Tuple, Optional

# Set clean style
plt.style.use('seaborn-v0_8-whitegrid')
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['font.size'] = 10

logger = logging.getLogger(__name__)

class JointErrorAnalyzer:
    """关节角误差分析器"""
    
    def __init__(self, norm_stats_path: Optional[str] = None):
        self.norm_stats = None
        self.joint_names = [
            'Left_J1', 'Left_J2', 'Left_J3', 'Left_J4', 'Left_J5', 'Left_J6', 'Left_Gripper',
            'Right_J1', 'Right_J2', 'Right_J3', 'Right_J4', 'Right_J5', 'Right_J6', 'Right_Gripper'
        ]
        
        if norm_stats_path:
            self.load_normalization_stats(norm_stats_path)
    
    def load_normalization_stats(self, norm_stats_path: str) -> bool:
        """加载归一化统计信息"""
        try:
            from openpi.shared import normalize
            stats_dir = Path(norm_stats_path).parent if Path(norm_stats_path).is_file() else Path(norm_stats_path)
            self.norm_stats = normalize.load(stats_dir)
            logger.info(f"成功加载归一化统计信息: {stats_dir}")
            return True
        except Exception as e:
            logger.warning(f"加载归一化统计信息失败: {e}")
            self.norm_stats = None
            return False
    
    def unnormalize_data(self, data: np.ndarray, data_type: str = "actions") -> np.ndarray:
        """反归一化数据到原始关节角度空间"""
        if self.norm_stats is None or data_type not in self.norm_stats:
            logger.warning(f"未找到{data_type}的归一化统计信息，返回原始数据")
            return data
        
        stats = self.norm_stats[data_type]
        # 使用z-score反归一化: x = normalized_x * std + mean
        data_shape = data.shape
        data_flat = data.reshape(-1, data_shape[-1])
        unnorm_flat = data_flat * (stats.std[:data_shape[-1]] + 1e-6) + stats.mean[:data_shape[-1]]
        return unnorm_flat.reshape(data_shape)
    
    def convert_to_degrees(self, radians_data: np.ndarray) -> np.ndarray:
        """将弧度转换为度数"""
        return np.degrees(radians_data)
    
    def calculate_comprehensive_errors(self, gt_data: np.ndarray, pred_data: np.ndarray, 
                                     prediction_step: int = 0) -> Dict:
        """计算全面的关节角误差统计"""
        
        # 处理预测数据形状
        if len(pred_data.shape) == 4:  # (timesteps, future_steps, action_horizon, action_dim)
            pred_data = pred_data[:, :, prediction_step, :14]
        else:
            pred_data = pred_data[..., :14]
        
        gt_data = gt_data[..., :14]
        
        # 反归一化到原始关节角度空间
        gt_unnorm = self.unnormalize_data(gt_data.reshape(-1, 14), "actions")
        pred_unnorm = self.unnormalize_data(pred_data.reshape(-1, 14), "actions")
        
        # 重新整形
        gt_unnorm = gt_unnorm.reshape(gt_data.shape)
        pred_unnorm = pred_unnorm.reshape(pred_data.shape)
        
        # 计算误差（弧度和度数）
        errors_rad = pred_unnorm - gt_unnorm
        abs_errors_rad = np.abs(errors_rad)
        errors_deg = self.convert_to_degrees(errors_rad)
        abs_errors_deg = self.convert_to_degrees(abs_errors_rad)
        
        # 计算每个关节的详细统计
        joint_stats = {}
        overall_stats = {
            'total_samples': errors_rad.size // 14,
            'timesteps': gt_data.shape[0],
            'future_steps': gt_data.shape[1] if len(gt_data.shape) > 2 else 1
        }
        
        for joint_idx in range(14):
            joint_name = self.joint_names[joint_idx]
            
            # 获取该关节的所有误差数据
            joint_errors_rad = errors_rad[:, :, joint_idx].flatten() if len(errors_rad.shape) > 2 else errors_rad[:, joint_idx].flatten()
            joint_abs_errors_rad = abs_errors_rad[:, :, joint_idx].flatten() if len(abs_errors_rad.shape) > 2 else abs_errors_rad[:, joint_idx].flatten()
            joint_errors_deg = errors_deg[:, :, joint_idx].flatten() if len(errors_deg.shape) > 2 else errors_deg[:, joint_idx].flatten()
            joint_abs_errors_deg = abs_errors_deg[:, :, joint_idx].flatten() if len(abs_errors_deg.shape) > 2 else abs_errors_deg[:, joint_idx].flatten()
            
            # GT数据范围
            gt_joint_data = gt_unnorm[:, :, joint_idx].flatten() if len(gt_unnorm.shape) > 2 else gt_unnorm[:, joint_idx].flatten()
            
            joint_stats[joint_name] = {
                'mae_rad': np.mean(joint_abs_errors_rad),
                'mae_deg': np.mean(joint_abs_errors_deg),
                'rmse_rad': np.sqrt(np.mean(joint_errors_rad**2)),
                'rmse_deg': np.sqrt(np.mean(joint_errors_deg**2)),
                'std_rad': np.std(joint_errors_rad),
                'std_deg': np.std(joint_errors_deg),
                'max_error_rad': np.max(joint_abs_errors_rad),
                'max_error_deg': np.max(joint_abs_errors_deg),
                'percentile_95_rad': np.percentile(joint_abs_errors_rad, 95),
                'percentile_95_deg': np.percentile(joint_abs_errors_deg, 95),
                'percentile_99_rad': np.percentile(joint_abs_errors_rad, 99),
                'percentile_99_deg': np.percentile(joint_abs_errors_deg, 99),
                'gt_range_rad': np.max(gt_joint_data) - np.min(gt_joint_data),
                'gt_range_deg': self.convert_to_degrees(np.max(gt_joint_data) - np.min(gt_joint_data)),
                'gt_mean_rad': np.mean(gt_joint_data),
                'gt_mean_deg': self.convert_to_degrees(np.mean(gt_joint_data)),
                'gt_std_rad': np.std(gt_joint_data),
                'gt_std_deg': self.convert_to_degrees(np.std(gt_joint_data)),
                'errors_rad': joint_errors_rad,
                'errors_deg': joint_errors_deg,
                'abs_errors_rad': joint_abs_errors_rad,
                'abs_errors_deg': joint_abs_errors_deg,
            }
            
            # 计算相对误差百分比
            if joint_stats[joint_name]['gt_range_deg'] > 0:
                joint_stats[joint_name]['relative_mae_percent'] = (joint_stats[joint_name]['mae_deg'] / joint_stats[joint_name]['gt_range_deg']) * 100
                joint_stats[joint_name]['relative_rmse_percent'] = (joint_stats[joint_name]['rmse_deg'] / joint_stats[joint_name]['gt_range_deg']) * 100
            else:
                joint_stats[joint_name]['relative_mae_percent'] = 0
                joint_stats[joint_name]['relative_rmse_percent'] = 0
        
        return {
            'joint_stats': joint_stats,
            'overall_stats': overall_stats,
            'errors_rad': errors_rad,
            'errors_deg': errors_deg,
            'gt_unnorm': gt_unnorm,
            'pred_unnorm': pred_unnorm
        }
    
    def print_detailed_analysis(self, error_analysis: Dict, prediction_step: int = 0):
        """打印详细的误差分析报告"""
        joint_stats = error_analysis['joint_stats']
        overall_stats = error_analysis['overall_stats']
        
        print("\n" + "="*130)
        print(f"🔧 详细关节角误差分析报告 (使用第{prediction_step}步预测)")
        print("="*130)
        print(f"总样本数: {overall_stats['total_samples']:,}")
        print(f"时间步数: {overall_stats['timesteps']}")
        print(f"预测步长: {overall_stats['future_steps']}")
        print("-"*130)
        
        # 表头
        print(f"{'关节名称':<15} {'MAE(度)':<10} {'RMSE(度)':<11} {'最大误差(度)':<12} {'95%分位(度)':<12} "
              f"{'99%分位(度)':<12} {'GT范围(度)':<12} {'相对MAE%':<10} {'相对RMSE%':<11}")
        print("-"*130)
        
        # 统计汇总变量
        total_mae_deg = 0
        total_rmse_deg = 0
        total_relative_mae = 0
        total_relative_rmse = 0
        
        # 打印每个关节的统计信息
        for joint_name, stats in joint_stats.items():
            print(f"{joint_name:<15} {stats['mae_deg']:<10.3f} {stats['rmse_deg']:<11.3f} "
                  f"{stats['max_error_deg']:<12.3f} {stats['percentile_95_deg']:<12.3f} "
                  f"{stats['percentile_99_deg']:<12.3f} {stats['gt_range_deg']:<12.3f} "
                  f"{stats['relative_mae_percent']:<10.2f} {stats['relative_rmse_percent']:<11.2f}")
            
            total_mae_deg += stats['mae_deg']
            total_rmse_deg += stats['rmse_deg']
            total_relative_mae += stats['relative_mae_percent']
            total_relative_rmse += stats['relative_rmse_percent']
        
        print("-"*130)
        print(f"{'平均值':<15} {total_mae_deg/14:<10.3f} {total_rmse_deg/14:<11.3f} "
              f"{'N/A':<12} {'N/A':<12} {'N/A':<12} {'N/A':<12} "
              f"{total_relative_mae/14:<10.2f} {total_relative_rmse/14:<11.2f}")
        
        # 分组统计
        print(f"\n📊 分组统计:")
        groups = {
            "左臂关节": [f'Left_J{i}' for i in range(1, 7)],
            "右臂关节": [f'Right_J{i}' for i in range(1, 7)],
            "夹爪": ['Left_Gripper', 'Right_Gripper']
        }
        
        for group_name, joint_list in groups.items():
            group_mae = np.mean([joint_stats[joint]['mae_deg'] for joint in joint_list if joint in joint_stats])
            group_rmse = np.mean([joint_stats[joint]['rmse_deg'] for joint in joint_list if joint in joint_stats])
            group_rel_mae = np.mean([joint_stats[joint]['relative_mae_percent'] for joint in joint_list if joint in joint_stats])
            group_rel_rmse = np.mean([joint_stats[joint]['relative_rmse_percent'] for joint in joint_list if joint in joint_stats])
            
            print(f"  {group_name}: MAE={group_mae:.3f}°, RMSE={group_rmse:.3f}°, "
                  f"相对MAE={group_rel_mae:.2f}%, 相对RMSE={group_rel_rmse:.2f}%")
        
        print("="*130)

    def create_error_distribution_plot(self, error_analysis: Dict, save_path: str = "joint_error_distribution.png"):
        """创建关节角误差分布图"""
        joint_stats = error_analysis['joint_stats']

        # 创建子图：2行7列，每个关节一个子图
        fig, axes = plt.subplots(2, 7, figsize=(28, 12))
        fig.suptitle('关节角误差分布 (度)', fontsize=16, fontweight='bold')

        for idx, (joint_name, stats) in enumerate(joint_stats.items()):
            row = idx // 7
            col = idx % 7
            ax = axes[row, col]

            # 绘制误差分布直方图
            errors_deg = stats['errors_deg']
            ax.hist(errors_deg, bins=50, alpha=0.7, color='skyblue', edgecolor='black', linewidth=0.5)

            # 添加统计线
            ax.axvline(0, color='red', linestyle='--', linewidth=2, label='零误差')
            ax.axvline(stats['mae_deg'], color='orange', linestyle='-', linewidth=2, label=f'MAE: {stats["mae_deg"]:.2f}°')
            ax.axvline(-stats['mae_deg'], color='orange', linestyle='-', linewidth=2)

            # 设置标题和标签
            ax.set_title(f'{joint_name}\nMAE: {stats["mae_deg"]:.2f}°, RMSE: {stats["rmse_deg"]:.2f}°', fontsize=10)
            ax.set_xlabel('误差 (度)', fontsize=9)
            ax.set_ylabel('频次', fontsize=9)
            ax.grid(True, alpha=0.3)

            if idx == 0:  # 只在第一个子图显示图例
                ax.legend(fontsize=8)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        logger.info(f"关节角误差分布图已保存到: {save_path}")
        return save_path
