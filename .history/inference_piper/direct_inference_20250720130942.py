#!/usr/bin/env python3
"""
直接推理工具 - 无需服务器，直接加载模型进行推理

使用示例:
conda activate openpi && python inference_piper/direct_inference.py \
    --checkpoint_dir checkpoints/lora_training/ae_lora_adamw_delta_action15_norm30_bat20_15w/62500 \
    --config_name lora_training \
    --default_prompt "pass me the drink" \
    --dataset_name "pass_drink" \
    --data_path "/home/<USER>/data/pass_drink/openpi" \
    --episode 10 \
    --step_limit 200 \
    --output inference_result/pass_drink_direct.png \
    --future_steps 15 \
    --prediction_step 14 \
    --action_horizon 15 \
    --multi_step_plot
"""

import os
import sys
import time
from pathlib import Path

# 获取当前文件的绝对路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_file))
sys.path.append(project_root)

import logging
import dataclasses
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image
import io
import argparse
import traceback

# 设置环境变量
os.environ['XLA_PYTHON_CLIENT_MEM_FRACTION'] = '0.7'
os.environ['XLA_PYTHON_CLIENT_PREALLOCATE'] = 'false'

# 导入OpenPI模块
from openpi.training import config as _config
from openpi.policies import policy_config as _policy_config
from openpi.models import pi0

# 设置日志
logging.basicConfig(level=logging.INFO, force=True)
logger = logging.getLogger(__name__)

@dataclasses.dataclass
class DirectInferenceConfig:
    """直接推理配置"""
    # 模型配置
    checkpoint_dir: str
    config_name: str
    default_prompt: str
    dataset_name: str
    data_path: str
    action_horizon: int = 15
    
    # 推理配置
    episode: int = 0
    step_limit: int = 200
    output: str = "direct_inference.png"
    future_steps: int = 15
    prediction_step: int = 14
    max_timesteps: int = 200
    prediction_horizon: int = 14
    multi_step_plot: bool = False
    max_errors: int = 1
    convert_bgr_to_rgb: bool = True
    debug: bool = False

def create_trained_model_config(config_name: str, dataset_name: str, data_path: str, default_prompt: str, action_horizon: int = 15):
    """创建与训练时相同的模型配置"""
    from lora_training.lora_train_config import CustomDataConfig
    from openpi.training.weight_loaders import CheckpointWeightLoader
    
    config = _config.TrainConfig(
        name=config_name,
        model=pi0.Pi0Config(action_dim=32, action_horizon=action_horizon),
        data=CustomDataConfig(
            data_path=data_path,
            dataset_name=dataset_name,
            default_prompt=default_prompt,
            use_delta_joint_actions=True,
        ),
        weight_loader=CheckpointWeightLoader("dummy"),  # 实际路径在create_policy中设置
        batch_size=1,
        num_train_steps=1,
        wandb_enabled=False,
    )
    return config

def load_normalization_stats(data_path: str, dataset_name: str, action_horizon: int = 15, checkpoint_dir: str = None):
    """加载归一化统计信息"""
    data_path_obj = Path(data_path)
    possible_paths = [
        f"./assets/{dataset_name}_del_step{action_horizon}/norm_stats.json",
        f"./assets/{data_path_obj.name}_del/norm_stats.json",
        f"./assets/{data_path_obj.name}_abs/norm_stats.json",
        "./assets/norm_stats.json",
    ]

    # 如果提供了检查点目录，也搜索检查点目录中的assets
    if checkpoint_dir:
        checkpoint_path = Path(checkpoint_dir)
        possible_paths.insert(0, str(checkpoint_path / "assets" / f"{dataset_name}_del_step{action_horizon}" / "norm_stats.json"))
        possible_paths.insert(1, str(checkpoint_path / "assets" / f"{data_path_obj.name}_del_step{action_horizon}" / "norm_stats.json"))
    
    for path in possible_paths:
        if Path(path).exists():
            try:
                from openpi.shared import normalize
                norm_stats = normalize.load(Path(path).parent)
                logger.info(f"成功加载归一化统计信息: {path}")
                return norm_stats
            except Exception as e:
                logger.warning(f"加载归一化统计信息失败: {e}")
                continue
    
    logger.warning("未找到归一化统计信息文件，将不进行反归一化")
    return None

def unnormalize_actions(actions, norm_stats):
    """反归一化动作数据"""
    if norm_stats is None or "actions" not in norm_stats:
        return actions
    
    stats = norm_stats["actions"]
    # 使用z-score反归一化: x = normalized_x * std + mean
    actions_array = np.array(actions)
    original_shape = actions_array.shape
    
    # 处理不同的数据形状
    if len(original_shape) == 3:  # (batch, horizon, dim)
        unnorm_actions = actions_array * (stats.std[:original_shape[-1]] + 1e-6) + stats.mean[:original_shape[-1]]
    elif len(original_shape) == 2:  # (horizon, dim)
        unnorm_actions = actions_array * (stats.std[:original_shape[-1]] + 1e-6) + stats.mean[:original_shape[-1]]
    else:  # (dim,)
        unnorm_actions = actions_array * (stats.std[:len(actions_array)] + 1e-6) + stats.mean[:len(actions_array)]
    
    return unnorm_actions

def decode_image(image_bytes, convert_bgr_to_rgb=False):
    """解码图像字节数据"""
    try:
        img = Image.open(io.BytesIO(image_bytes))
        img_array = np.array(img, dtype=np.uint8)
        
        if convert_bgr_to_rgb and len(img_array.shape) == 3 and img_array.shape[2] == 3:
            img_array = img_array[:, :, ::-1]  # BGR -> RGB
        
        return img_array
    except Exception as e:
        logger.error(f"图像解码失败: {e}")
        return None

class DirectInferenceRunner:
    """直接推理执行器"""
    def __init__(self, config: DirectInferenceConfig):
        self.config = config
        self.policy = None
        self.norm_stats = None
        self.gt_data = None
        
    def load_model(self):
        """加载模型"""
        try:
            logger.info(f"正在加载模型...")
            logger.info(f"检查点目录: {self.config.checkpoint_dir}")
            logger.info(f"配置名称: {self.config.config_name}")
            logger.info(f"数据集名称: {self.config.dataset_name}")
            logger.info(f"Action Horizon: {self.config.action_horizon}")
            
            # 创建配置
            train_config = create_trained_model_config(
                self.config.config_name,
                self.config.dataset_name,
                self.config.data_path,
                self.config.default_prompt,
                self.config.action_horizon
            )
            
            # 创建训练好的策略
            self.policy = _policy_config.create_trained_policy(
                train_config,
                self.config.checkpoint_dir,
                default_prompt=self.config.default_prompt
            )
            
            # 加载归一化统计信息
            self.norm_stats = load_normalization_stats(
                self.config.data_path, 
                self.config.dataset_name, 
                self.config.action_horizon
            )
            
            logger.info("模型加载成功!")
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            traceback.print_exc()
            return False
    
    def load_gt_data(self):
        """加载真实数据"""
        try:
            data_path = Path(self.config.data_path)
            parquet_files = list(data_path.glob("data/chunk-*/episode_*.parquet"))
            if not parquet_files:
                parquet_files = list(data_path.glob("episode_*.parquet"))
                if not parquet_files:
                    logger.error("未找到parquet文件")
                    return False
            
            # 加载指定episode的数据
            target_episode = self.config.episode
            episode_file = None
            
            for file_path in parquet_files:
                try:
                    df = pd.read_parquet(file_path)
                    if len(df) > 0:
                        episode_idx = int(file_path.stem.split('_')[-1])
                        if episode_idx == target_episode:
                            episode_file = file_path
                            break
                except Exception as e:
                    logger.warning(f"无法读取文件 {file_path}: {e}")
                    continue
            
            if episode_file is None:
                logger.error(f"未找到episode {target_episode}的数据")
                return False
            
            # 加载数据
            df = pd.read_parquet(episode_file)
            
            # 提取状态和图像数据
            state_columns = [col for col in df.columns if col.startswith('observation.state')]
            image_columns = [col for col in df.columns if 'image' in col.lower()]
            
            states = []
            images_data = []
            
            for idx, row in df.iterrows():
                # 提取状态
                state = []
                for col in sorted(state_columns):
                    state.append(row[col])
                states.append(state[:14])  # 只取前14维
                
                # 提取图像
                images = {}
                for col in image_columns:
                    if '.' in col:
                        cam_name = col.split('.')[-1]
                    else:
                        cam_name = col
                    
                    img_data = row[col]
                    if img_data is not None and isinstance(img_data, dict) and 'bytes' in img_data:
                        images[cam_name] = img_data['bytes']
                
                images_data.append(images)
            
            self.gt_data = {
                'states': states,
                'images': images_data,
                'df': df,
                'episode_file': episode_file,
                'length': len(states)
            }
            
            logger.info(f"成功加载episode {target_episode}，共 {len(states)} 个时间步")
            return True
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            traceback.print_exc()
            return False

    def run_inference(self):
        """执行推理"""
        try:
            if not self.gt_data:
                logger.error("未加载数据")
                return None

            states = self.gt_data['states']
            images_data = self.gt_data['images']

            max_steps = min(self.config.step_limit, len(states) - self.config.future_steps)

            gt_states = []
            pred_states = []
            inference_times = []

            logger.info(f"开始推理，共 {max_steps} 步...")

            for step_idx in range(max_steps):
                try:
                    # 获取当前状态
                    current_state = states[step_idx]
                    padded_state = np.zeros(32, dtype=np.float32)
                    padded_state[:len(current_state)] = current_state

                    # 获取图像数据
                    images = {}
                    step_images = images_data[step_idx]
                    for cam_name, img_bytes in step_images.items():
                        img_array = decode_image(img_bytes, self.config.convert_bgr_to_rgb)
                        if img_array is not None:
                            images[cam_name] = img_array

                    # 准备观测数据
                    obs = {
                        "state": padded_state,
                        "image": images,
                        "prompt": self.config.default_prompt,
                        "tokenized_prompt": np.zeros((1, 48), dtype=np.int32),
                        "tokenized_prompt_mask": np.ones((1, 48), dtype=bool)
                    }

                    # 执行推理
                    start_time = time.time()
                    result = self.policy.infer(obs)
                    inference_time = time.time() - start_time

                    # 获取预测动作
                    if "actions" in result:
                        pred_actions = result["actions"]

                        # 反归一化
                        if self.norm_stats is not None:
                            pred_actions = unnormalize_actions(pred_actions, self.norm_stats)

                        # 记录结果
                        gt_states.append(current_state)
                        pred_states.append(pred_actions)
                        inference_times.append(inference_time)

                        if step_idx % 10 == 0:
                            logger.info(f"步骤 {step_idx}/{max_steps} 完成，推理时间: {inference_time:.3f}s")

                except Exception as e:
                    logger.error(f"步骤 {step_idx} 推理失败: {e}")
                    if self.config.debug:
                        traceback.print_exc()
                    continue

            if not gt_states or not pred_states:
                logger.error("没有成功的推理步骤")
                return None

            results = {
                'gt_states': np.array(gt_states),
                'pred_states': np.array(pred_states),
                'inference_times': np.array(inference_times),
                'episode_idx': self.config.episode,
                'total_steps': len(gt_states),
                'future_steps': self.config.future_steps
            }

            logger.info(f"推理完成: {len(gt_states)} 步成功")
            return results

        except Exception as e:
            logger.error(f"推理执行失败: {e}")
            traceback.print_exc()
            return None

    def create_visualization(self, results):
        """创建可视化"""
        try:
            gt_states = results['gt_states']
            pred_states = results['pred_states']

            # 创建基本的误差分析图
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle(f'Direct Inference Results - Episode {self.config.episode}', fontsize=16)

            # 计算误差
            if len(pred_states.shape) == 3:  # (steps, horizon, dim)
                # 使用指定的预测步骤
                pred_step = min(self.config.prediction_step, pred_states.shape[1] - 1)
                pred_actions = pred_states[:, pred_step, :14]
            else:  # (steps, dim)
                pred_actions = pred_states[:, :14]

            gt_actions = gt_states[:, :14]
            errors = np.abs(pred_actions - gt_actions)

            # 1. 关节角度对比
            axes[0, 0].plot(gt_actions[:, 0], label='GT Joint 0', alpha=0.7)
            axes[0, 0].plot(pred_actions[:, 0], label='Pred Joint 0', alpha=0.7)
            axes[0, 0].set_title('Joint 0 Comparison')
            axes[0, 0].legend()
            axes[0, 0].grid(True)

            # 2. 误差分布
            axes[0, 1].boxplot([errors[:, i] for i in range(min(6, errors.shape[1]))],
                              labels=[f'J{i}' for i in range(min(6, errors.shape[1]))])
            axes[0, 1].set_title('Joint Error Distribution')
            axes[0, 1].set_ylabel('Absolute Error (rad)')

            # 3. 时间序列误差
            mean_error = np.mean(errors, axis=1)
            axes[1, 0].plot(mean_error)
            axes[1, 0].set_title('Mean Error Over Time')
            axes[1, 0].set_xlabel('Time Step')
            axes[1, 0].set_ylabel('Mean Absolute Error (rad)')
            axes[1, 0].grid(True)

            # 4. 推理时间
            axes[1, 1].plot(results['inference_times'])
            axes[1, 1].set_title('Inference Time')
            axes[1, 1].set_xlabel('Time Step')
            axes[1, 1].set_ylabel('Time (s)')
            axes[1, 1].grid(True)

            plt.tight_layout()
            plt.savefig(self.config.output, dpi=300, bbox_inches='tight')
            logger.info(f"可视化结果保存到: {self.config.output}")

            # 打印统计信息
            logger.info(f"平均绝对误差: {np.mean(errors):.4f} rad ({np.degrees(np.mean(errors)):.2f} deg)")
            logger.info(f"平均推理时间: {np.mean(results['inference_times']):.3f}s")

        except Exception as e:
            logger.error(f"可视化创建失败: {e}")
            traceback.print_exc()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="直接推理工具")
    
    # 模型配置
    parser.add_argument("--checkpoint_dir", required=True, help="检查点目录路径")
    parser.add_argument("--config_name", required=True, help="配置名称")
    parser.add_argument("--default_prompt", required=True, help="默认提示词")
    parser.add_argument("--dataset_name", required=True, help="数据集名称")
    parser.add_argument("--data_path", required=True, help="数据集路径")
    parser.add_argument("--action_horizon", type=int, default=15, help="动作视野长度")
    
    # 推理配置
    parser.add_argument("--episode", type=int, default=0, help="要分析的剧集索引")
    parser.add_argument("--step_limit", type=int, default=200, help="步骤限制")
    parser.add_argument("--output", default="direct_inference.png", help="输出图像文件")
    parser.add_argument("--future_steps", type=int, default=15, help="未来步数")
    parser.add_argument("--prediction_step", type=int, default=14, help="预测步数")
    parser.add_argument("--max_timesteps", type=int, default=200, help="最大时间步")
    parser.add_argument("--prediction_horizon", type=int, default=14, help="预测视野")
    parser.add_argument("--multi_step_plot", action="store_true", help="创建多步预测图")
    parser.add_argument("--max_errors", type=int, default=1, help="最大错误数")
    parser.add_argument("--convert_bgr_to_rgb", action="store_true", default=True, help="BGR转RGB")
    parser.add_argument("--debug", action="store_true", help="调试模式")
    
    args = parser.parse_args()
    
    # 创建配置
    config = DirectInferenceConfig(**vars(args))
    
    # 创建推理执行器
    runner = DirectInferenceRunner(config)
    
    try:
        # 加载模型
        if not runner.load_model():
            return 1
        
        # 加载数据
        if not runner.load_gt_data():
            return 1
        
        # 执行推理
        results = runner.run_inference()
        if results is None:
            logger.error("推理失败")
            return 1

        # 生成可视化
        runner.create_visualization(results)

        logger.info("推理完成!")
        return 0
        
    except KeyboardInterrupt:
        logger.info("用户中断")
        return 1
    except Exception as e:
        logger.error(f"执行失败: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
