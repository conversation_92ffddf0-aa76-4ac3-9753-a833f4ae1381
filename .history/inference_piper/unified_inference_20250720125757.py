#!/usr/bin/env python3
"""
统一的OpenPI推理工具 - 集成服务器启动和推理分析

使用示例:
conda activate openpi && python inference_piper/unified_inference.py \
    --checkpoint_dir checkpoints/lora_training/ae_lora_adamw_delta_action15_norm30_bat20_15w/62500 \
    --config_name lora_training \
    --default_prompt "pass me the drink" \
    --dataset_name "pass_drink" \
    --data_path "/home/<USER>/data/pass_drink/openpi" \
    --episode 10 \
    --step_limit 200 \
    --output inference_result/pass_drink_action15_unified.png \
    --future_steps 15 \
    --prediction_step 14 \
    --multi_step_plot \
    --max_timesteps 200 \
    --prediction_horizon 14 \
    --action_horizon 15 \
    --port 8000 \
    --host localhost
"""
import os
import sys
import time
import threading
import signal
from pathlib import Path

# 获取当前文件的绝对路径
current_file = os.path.abspath(__file__)
project_root = os.path.dirname(os.path.dirname(current_file))
sys.path.append(project_root)

import logging
import dataclasses
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from PIL import Image
import io
import argparse
import traceback

# 设置环境变量
os.environ['XLA_PYTHON_CLIENT_MEM_FRACTION'] = '0.7'
os.environ['XLA_PYTHON_CLIENT_PREALLOCATE'] = 'false'

# 导入OpenPI模块
from openpi.training import config as _config
from openpi.policies import policy_config as _policy_config
from openpi.models import pi0
from openpi.serving import websocket_policy_server
from openpi_client import websocket_client_policy
import socket

# 设置日志
logging.basicConfig(level=logging.INFO, force=True)
logger = logging.getLogger(__name__)

@dataclasses.dataclass
class UnifiedConfig:
    """统一配置"""
    # 服务器配置
    checkpoint_dir: str
    config_name: str
    default_prompt: str
    dataset_name: str
    data_path: str
    port: int = 8000
    host: str = "localhost"

    # 模型配置
    action_horizon: int = 15

    # 推理配置
    episode: int = 0
    step_limit: int = 2000
    output: str = "unified_inference.png"
    future_steps: int = 15
    prediction_step: int = 14
    max_timesteps: int = 2000
    prediction_horizon: int = 14
    multi_step_plot: bool = False
    max_errors: int = 1
    convert_bgr_to_rgb: bool = True
    debug: bool = False

def create_trained_model_config(config_name: str, dataset_name: str, data_path: str, default_prompt: str, action_horizon: int = 15):
    """创建与训练时相同的模型配置"""
    from lora_training.lora_train_config import CustomDataConfig
    from openpi.training.weight_loaders import CheckpointWeightLoader

    config = _config.TrainConfig(
        name=config_name,
        model=pi0.Pi0Config(action_dim=32, action_horizon=action_horizon),
        data=CustomDataConfig(
            data_path=data_path,
            dataset_name=dataset_name,
            default_prompt=default_prompt,
            use_delta_joint_actions=True,
        ),
        weight_loader=CheckpointWeightLoader("dummy"),  # 实际路径在create_policy中设置
        batch_size=1,
        num_train_steps=1,
        wandb_enabled=False,
    )
    return config

def create_policy_with_unnormalize(model_config: UnifiedConfig):
    """创建带反归一化的策略"""
    logger.info(f"正在加载模型...")
    logger.info(f"检查点目录: {model_config.checkpoint_dir}")
    logger.info(f"配置名称: {model_config.config_name}")
    logger.info(f"数据集名称: {model_config.dataset_name}")
    
    # 创建配置
    config = create_trained_model_config(
        model_config.config_name,
        model_config.dataset_name,
        model_config.data_path,
        model_config.default_prompt,
        model_config.action_horizon
    )
    
    # 创建训练好的策略
    base_policy = _policy_config.create_trained_policy(
        config,
        model_config.checkpoint_dir,
        default_prompt=model_config.default_prompt
    )
    
    # 加载归一化统计信息
    norm_stats = load_normalization_stats(model_config.data_path, model_config.dataset_name)
    
    # 创建带反归一化的策略包装器
    policy = UnnormalizePolicy(base_policy, norm_stats)
    
    logger.info("模型加载成功!")
    return policy

def load_normalization_stats(data_path: str, dataset_name: str):
    """加载归一化统计信息"""
    data_path_obj = Path(data_path)
    possible_paths = [
        f"./assets/{dataset_name}_del_step15/norm_stats.json",
      
    ]
    
    for path in possible_paths:
        if Path(path).exists():
            try:
                from openpi.shared import normalize
                norm_stats = normalize.load(Path(path).parent)
                logger.info(f"成功加载归一化统计信息: {path}")
                return norm_stats
            except Exception as e:
                logger.warning(f"加载归一化统计信息失败: {e}")
                continue
    
    logger.warning("未找到归一化统计信息文件，将不进行反归一化")
    return None

class UnnormalizePolicy:
    """带反归一化的策略包装器"""
    def __init__(self, base_policy, norm_stats):
        self.base_policy = base_policy
        self.norm_stats = norm_stats
        self.metadata = base_policy.metadata
    
    def infer(self, obs):
        """执行推理并反归一化结果"""
        # 调用原始策略
        result = self.base_policy.infer(obs)
        
        # 如果有归一化统计信息，进行反归一化
        if self.norm_stats is not None and "actions" in result:
            result["actions"] = self.unnormalize_actions(result["actions"])
        
        return result
    
    def unnormalize_actions(self, actions):
        """反归一化动作数据"""
        if self.norm_stats is None or "actions" not in self.norm_stats:
            return actions
        
        stats = self.norm_stats["actions"]
        # 使用z-score反归一化: x = normalized_x * std + mean
        actions_array = np.array(actions)
        original_shape = actions_array.shape
        
        # 处理不同的数据形状
        if len(original_shape) == 3:  # (batch, horizon, dim)
            unnorm_actions = actions_array * (stats.std[:original_shape[-1]] + 1e-6) + stats.mean[:original_shape[-1]]
        elif len(original_shape) == 2:  # (horizon, dim)
            unnorm_actions = actions_array * (stats.std[:original_shape[-1]] + 1e-6) + stats.mean[:original_shape[-1]]
        else:  # (dim,)
            unnorm_actions = actions_array * (stats.std[:len(actions_array)] + 1e-6) + stats.mean[:len(actions_array)]
        
        return unnorm_actions.tolist()
    
    def reset(self):
        """重置策略"""
        if hasattr(self.base_policy, 'reset'):
            self.base_policy.reset()

class UnifiedInferenceServer:
    """统一推理服务器"""
    def __init__(self, config: UnifiedConfig):
        self.config = config
        self.server = None
        self.server_thread = None
        self.policy = None
        
    def start_server(self):
        """启动服务器"""
        try:
            # 创建策略
            self.policy = create_policy_with_unnormalize(self.config)
            
            # 获取策略元数据
            policy_metadata = self.policy.metadata
            
            # 创建WebSocket服务器
            self.server = websocket_policy_server.WebsocketPolicyServer(
                policy=self.policy,
                host=self.config.host,
                port=self.config.port,
                metadata=policy_metadata,
            )
            
            logger.info("=" * 60)
            logger.info(f"🚀 OpenPI推理服务器已启动!")
            logger.info(f"📍 地址: {self.config.host}:{self.config.port}")
            logger.info(f"🤖 模型: {self.config.config_name}")
            logger.info(f"📂 检查点: {self.config.checkpoint_dir}")
            logger.info(f"💬 默认提示: {self.config.default_prompt}")
            logger.info("=" * 60)
            
            # 在单独线程中启动服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            
            # 等待服务器启动
            time.sleep(3)
            logger.info("服务器启动完成，准备开始推理...")
            
            return True
            
        except Exception as e:
            logger.error(f"服务器启动失败: {e}")
            traceback.print_exc()
            return False
    
    def stop_server(self):
        """停止服务器"""
        if self.server:
            try:
                self.server.shutdown()
                logger.info("服务器已停止")
            except Exception as e:
                logger.error(f"停止服务器时出错: {e}")

def decode_image(image_bytes, convert_bgr_to_rgb=False):
    """解码图像字节数据"""
    try:
        img = Image.open(io.BytesIO(image_bytes))
        img_array = np.array(img, dtype=np.uint8)

        if convert_bgr_to_rgb and len(img_array.shape) == 3 and img_array.shape[2] == 3:
            img_array = img_array[:, :, ::-1]  # BGR -> RGB

        return img_array
    except Exception as e:
        logger.error(f"图像解码失败: {e}")
        return None

def run_inference_analysis(config: UnifiedConfig):
    """运行推理分析"""
    try:
        # 导入推理可视化器（从原始文件复制核心逻辑）
        from inference_piper.inference_with_dis_openloop import FullEpisodeInferenceVisualizer

        # 创建可视化器（不需要归一化统计，因为服务器端已经反归一化了）
        visualizer = FullEpisodeInferenceVisualizer(
            host=config.host,
            port=config.port,
            data_path=config.data_path,
            debug=config.debug,
            prediction_step=config.prediction_step,
            convert_bgr_to_rgb=config.convert_bgr_to_rgb,
            norm_stats_path=None  # 不需要，服务器端已处理
        )

        # 连接服务器
        if not visualizer.connect_to_server():
            logger.error("无法连接到推理服务器")
            return False

        # 加载数据
        if not visualizer.load_gt_data(target_episode=config.episode):
            logger.error("无法加载地面真值数据")
            return False

        logger.info(f"开始为剧集 {config.episode} 进行完整分析...")

        # 运行完整剧集推理
        results = visualizer.run_full_episode_inference(
            episode_idx=0,
            step_limit=config.step_limit,
            max_consecutive_errors=config.max_errors,
            future_steps=config.future_steps
        )

        if results is None:
            logger.error("推理失败，无法生成结果")
            return False

        # 创建可视化
        visualizer.create_full_episode_plot(results, config.output)
        logger.info(f"完整剧集可视化已完成！结果保存到: {config.output}")

        # 如果需要，创建多时刻预测轨迹对比图
        if config.multi_step_plot:
            multi_step_output = config.output.replace('.png', '_multi_step.png')
            visualizer.create_multi_step_comparison_plot(
                results,
                multi_step_output,
                config.max_timesteps,
                config.prediction_horizon
            )
            logger.info(f"多时刻预测轨迹图已完成！结果保存到: {multi_step_output}")

        return True

    except Exception as e:
        logger.error(f"推理分析失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="统一的OpenPI推理工具")
    
    # 服务器配置
    parser.add_argument("--checkpoint_dir", required=True, help="检查点目录路径")
    parser.add_argument("--config_name", required=True, help="配置名称")
    parser.add_argument("--default_prompt", required=True, help="默认提示词")
    parser.add_argument("--dataset_name", required=True, help="数据集名称")
    parser.add_argument("--data_path", required=True, help="数据集路径")
    parser.add_argument("--port", type=int, default=8000, help="服务器端口")
    parser.add_argument("--host", default="localhost", help="服务器主机地址")
    
    # 推理配置
    parser.add_argument("--episode", type=int, default=0, help="要分析的剧集索引")
    parser.add_argument("--step_limit", type=int, default=2000, help="步骤限制")
    parser.add_argument("--output", default="unified_inference.png", help="输出图像文件")
    parser.add_argument("--future_steps", type=int, default=15, help="未来步数")
    parser.add_argument("--prediction_step", type=int, default=14, help="预测步数")
    parser.add_argument("--max_timesteps", type=int, default=2000, help="最大时间步")
    parser.add_argument("--prediction_horizon", type=int, default=14, help="预测视野")
    parser.add_argument("--multi_step_plot", action="store_true", help="创建多步预测图")
    parser.add_argument("--max_errors", type=int, default=1, help="最大错误数")
    parser.add_argument("--convert_bgr_to_rgb", action="store_true", default=True, help="BGR转RGB")
    parser.add_argument("--debug", action="store_true", help="调试模式")
    
    args = parser.parse_args()
    
    # 创建统一配置
    config = UnifiedConfig(**vars(args))
    
    # 创建统一推理服务器
    server = UnifiedInferenceServer(config)
    
    try:
        # 启动服务器
        if not server.start_server():
            return 1
        
        # 运行推理分析
        success = run_inference_analysis(config)

        if success:
            logger.info("推理分析完成!")
        else:
            logger.error("推理分析失败!")
            return 1
            
    except KeyboardInterrupt:
        logger.info("收到停止信号...")
    finally:
        server.stop_server()

if __name__ == "__main__":
    main()
