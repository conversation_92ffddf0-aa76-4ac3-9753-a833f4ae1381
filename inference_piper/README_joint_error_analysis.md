# OpenPI 关节角误差分析工具

这个工具集提供了详细的关节角误差分析功能，可以将OpenPI推理结果转换为实际的关节角度误差，并提供量化的数据展示。

## 功能特性

### 🔧 核心功能
- **关节角度反归一化**: 将归一化的推理结果转换回原始关节角度空间
- **弧度到度数转换**: 提供更直观的度数单位误差分析
- **多维度误差统计**: MAE、RMSE、最大误差、百分位数等
- **分组统计**: 左臂、右臂、夹爪分组分析
- **相对误差计算**: 相对于关节运动范围的误差百分比

### 📊 可视化功能
- **误差分布图**: 每个关节的误差分布直方图
- **误差对比图**: 多维度对比各关节误差特征
- **时间序列误差**: 误差随时间变化的趋势
- **累积误差分布**: 整体误差的累积概率分布

## 使用方法

### 1. 基础使用

```bash
# 运行基础的关节角误差分析
python run_joint_error_analysis.py \
    --data_path /path/to/your/data \
    --episode 0 \
    --step_limit 100
```

### 2. 完整功能使用

```bash
# 运行完整的关节角误差分析（包含所有图表）
python run_joint_error_analysis.py \
    --host localhost \
    --port 8000 \
    --data_path /home/<USER>/data/pick_and_place_eggplant/openpi \
    --episode 0 \
    --step_limit 200 \
    --output_base detailed_analysis \
    --norm_stats_path ./assets/pick_and_place_eggplant_del/norm_stats.json \
    --prediction_step 0 \
    --future_steps 30
```

### 3. 直接使用推理脚本

```bash
# 使用增强的推理脚本
python inference_with_dis_openloop.py \
    --data_path /path/to/data \
    --episode 0 \
    --step_limit 100 \
    --output analysis_result.png \
    --detailed_error_analysis \
    --norm_stats_path ./assets/norm_stats.json \
    --prediction_step 0
```

## 参数说明

### 数据参数
- `--data_path`: OpenPI数据集路径
- `--episode`: 要分析的剧集编号
- `--step_limit`: 分析的时间步数限制

### 分析参数
- `--norm_stats_path`: 归一化统计信息文件路径（用于反归一化）
- `--prediction_step`: 选择多步预测中的哪一步进行分析（0=第一步，-1=最后一步）
- `--future_steps`: 预测的未来步骤数量

### 输出参数
- `--output_base`: 输出文件的基础名称
- `--detailed_error_analysis`: 启用详细误差分析图表
- `--multi_step_plot`: 启用多步预测轨迹图

## 输出文件说明

运行分析后会生成以下文件：

### 1. 基础推理对比图 (`analysis_result.png`)
- 显示每个关节的GT vs 预测值对比
- 包含MAE和相关系数信息

### 2. 多步预测轨迹图 (`analysis_result_multi_step.png`)
- 显示不同时刻的预测轨迹
- 帮助理解预测的时间一致性

### 3. 关节角误差分布图 (`analysis_result_error_distribution.png`)
- 每个关节的误差分布直方图
- 显示误差的统计特征

### 4. 关节角误差对比图 (`analysis_result_error_comparison.png`)
- MAE vs RMSE对比
- 相对误差百分比
- 分组箱线图
- 累积误差分布

## 误差指标说明

### 基础指标
- **MAE (平均绝对误差)**: `mean(|pred - gt|)` - 预测值与真实值的平均绝对差值
- **RMSE (均方根误差)**: `sqrt(mean((pred - gt)²))` - 更重视大误差的评估指标
- **最大误差**: 单次预测的最大绝对误差
- **95%分位数**: 95%的误差都小于此值

### 相对指标
- **相对MAE%**: `(MAE / GT_range) × 100%` - 相对于关节运动范围的误差百分比
- **相对RMSE%**: `(RMSE / GT_range) × 100%` - 相对均方根误差百分比

### 分组统计
- **左臂关节**: Left_J1 到 Left_J6 的平均误差
- **右臂关节**: Right_J1 到 Right_J6 的平均误差  
- **夹爪**: Left_Gripper 和 Right_Gripper 的平均误差

## 实际应用示例

### 示例1: 评估模型性能
```bash
# 分析训练好的模型在测试集上的表现
python run_joint_error_analysis.py \
    --data_path /home/<USER>/data/test_dataset/openpi \
    --episode 5 \
    --step_limit 500 \
    --output_base model_evaluation \
    --prediction_step 0
```

### 示例2: 对比不同预测步长
```bash
# 分析第0步预测
python run_joint_error_analysis.py --prediction_step 0 --output_base step_0_analysis

# 分析第10步预测  
python run_joint_error_analysis.py --prediction_step 10 --output_base step_10_analysis

# 分析最后一步预测
python run_joint_error_analysis.py --prediction_step -1 --output_base final_step_analysis
```

## 注意事项

1. **归一化统计信息**: 为了获得准确的关节角度误差，需要提供训练时使用的归一化统计信息文件
2. **数据单位**: 工具假设关节角度以弧度为单位，会自动转换为度数显示
3. **内存使用**: 大数据集分析可能需要较多内存，建议适当限制step_limit
4. **服务器连接**: 确保OpenPI推理服务器正在运行并可访问

## 故障排除

### 常见问题
1. **找不到归一化统计信息**: 工具会自动搜索常见路径，也可以手动指定
2. **导入错误**: 确保在正确的conda环境中运行
3. **内存不足**: 减少step_limit或使用更小的数据集

### 调试模式
添加 `--debug` 参数可以获得更详细的日志信息：
```bash
python inference_with_dis_openloop.py --debug --detailed_error_analysis
```
