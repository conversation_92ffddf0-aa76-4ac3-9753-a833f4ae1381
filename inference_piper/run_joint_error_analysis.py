#!/usr/bin/env python3
"""
关节角误差分析运行脚本
演示如何使用增强的OpenPI推理脚本进行详细的关节角误差分析
"""

import subprocess
import sys
import argparse
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_inference_with_error_analysis(
    host="localhost",
    port=8000,
    data_path="/home/<USER>/data/pick_and_place_eggplant/openpi",
    episode=0,
    step_limit=100,
    output_base="joint_error_analysis",
    norm_stats_path=None,
    prediction_step=0,
    future_steps=30,
    detailed_analysis=True,
    multi_step_plot=True
):
    """运行推理并生成详细的关节角误差分析"""
    
    # 构建命令
    cmd = [
        sys.executable, "inference_with_dis_openloop.py",
        "--host", host,
        "--port", str(port),
        "--data_path", data_path,
        "--episode", str(episode),
        "--step_limit", str(step_limit),
        "--output", f"{output_base}.png",
        "--prediction_step", str(prediction_step),
        "--future_steps", str(future_steps),
        "--debug"  # 启用调试模式
    ]
    
    # 添加可选参数
    if norm_stats_path:
        cmd.extend(["--norm_stats_path", norm_stats_path])
    
    if detailed_analysis:
        cmd.append("--detailed_error_analysis")
    
    if multi_step_plot:
        cmd.extend([
            "--multi_step_plot",
            "--max_timesteps", "100",
            "--prediction_horizon", "20"
        ])
    
    logger.info(f"运行命令: {' '.join(cmd)}")
    
    try:
        # 运行推理脚本
        result = subprocess.run(cmd, cwd=Path(__file__).parent, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("推理和误差分析完成成功！")
            logger.info("生成的文件:")
            
            # 列出生成的文件
            output_dir = Path(__file__).parent
            base_name = output_base
            
            possible_files = [
                f"{base_name}.png",
                f"{base_name}_multi_step.png",
                f"{base_name}_error_distribution.png",
                f"{base_name}_error_comparison.png"
            ]
            
            for file_name in possible_files:
                file_path = output_dir / file_name
                if file_path.exists():
                    logger.info(f"  ✓ {file_path}")
                else:
                    logger.info(f"  ✗ {file_path} (未生成)")
            
            print("\n" + "="*80)
            print("📊 关节角误差分析完成！")
            print("="*80)
            print("生成的分析图表:")
            print(f"  1. 基础推理对比图: {base_name}.png")
            if multi_step_plot:
                print(f"  2. 多步预测轨迹图: {base_name}_multi_step.png")
            if detailed_analysis:
                print(f"  3. 关节角误差分布图: {base_name}_error_distribution.png")
                print(f"  4. 关节角误差对比图: {base_name}_error_comparison.png")
            
            print("\n📈 分析说明:")
            print("  • MAE (平均绝对误差): 预测值与真实值的平均绝对差值")
            print("  • RMSE (均方根误差): 更重视大误差的评估指标")
            print("  • 相对误差%: 相对于关节运动范围的误差百分比")
            print("  • 95%分位数: 95%的误差都小于此值")
            print("  • 误差分布图: 显示每个关节的误差分布直方图")
            print("  • 误差对比图: 多维度对比各关节的误差特征")
            print("="*80)
            
        else:
            logger.error("推理脚本执行失败！")
            logger.error(f"错误输出: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"执行推理脚本时发生错误: {e}")
        return False
    
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行OpenPI关节角误差分析")
    
    # 服务器配置
    parser.add_argument("--host", default="localhost", help="推理服务器主机")
    parser.add_argument("--port", type=int, default=8000, help="推理服务器端口")
    
    # 数据配置
    parser.add_argument("--data_path", 
                        default="/home/<USER>/data/pick_and_place_eggplant/openpi",
                        help="数据路径")
    parser.add_argument("--episode", type=int, default=0, help="要分析的剧集索引")
    parser.add_argument("--step_limit", type=int, default=100, 
                        help="分析的步骤数量限制")
    
    # 输出配置
    parser.add_argument("--output_base", default="joint_error_analysis",
                        help="输出文件基础名称")
    parser.add_argument("--norm_stats_path", type=str, default=None,
                        help="归一化统计信息路径")
    
    # 分析配置
    parser.add_argument("--prediction_step", type=int, default=0,
                        help="选择哪一步预测进行分析")
    parser.add_argument("--future_steps", type=int, default=30,
                        help="预测的未来步骤数")
    parser.add_argument("--no_detailed_analysis", action="store_true",
                        help="禁用详细误差分析图")
    parser.add_argument("--no_multi_step_plot", action="store_true",
                        help="禁用多步预测图")
    
    args = parser.parse_args()
    
    # 自动查找归一化统计信息
    if args.norm_stats_path is None:
        data_path = Path(args.data_path)
        dataset_name = data_path.name
        
        possible_norm_paths = [
            f"./assets/{dataset_name}_del/norm_stats.json",
            f"./assets/{dataset_name}_abs/norm_stats.json",
            "./assets/norm_stats.json"
        ]
        
        for norm_path in possible_norm_paths:
            if Path(norm_path).exists():
                args.norm_stats_path = norm_path
                logger.info(f"自动找到归一化统计信息: {norm_path}")
                break
        
        if args.norm_stats_path is None:
            logger.warning("未找到归一化统计信息，将使用原始数值进行分析")
    
    print("🚀 开始运行OpenPI关节角误差分析...")
    print(f"数据路径: {args.data_path}")
    print(f"剧集: {args.episode}")
    print(f"步骤限制: {args.step_limit}")
    print(f"预测步骤: {args.prediction_step}")
    print(f"归一化统计: {args.norm_stats_path or '未使用'}")
    print("-" * 50)
    
    success = run_inference_with_error_analysis(
        host=args.host,
        port=args.port,
        data_path=args.data_path,
        episode=args.episode,
        step_limit=args.step_limit,
        output_base=args.output_base,
        norm_stats_path=args.norm_stats_path,
        prediction_step=args.prediction_step,
        future_steps=args.future_steps,
        detailed_analysis=not args.no_detailed_analysis,
        multi_step_plot=not args.no_multi_step_plot
    )
    
    if success:
        print("\n✅ 关节角误差分析完成！")
        return 0
    else:
        print("\n❌ 关节角误差分析失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
